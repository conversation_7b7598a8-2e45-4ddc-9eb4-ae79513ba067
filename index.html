<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>天津锐兵能源科技有限公司 - 全球化工资源整合者</title>
    <meta name="description" content="天津锐兵能源科技，依托中国兵器集团军民联合企业与全球供应链，为您提供高品质化工产品贸易、化工工程设计、全链条技术服务与领先的智慧园区解决方案。">
    <meta name="keywords" content="化工贸易, 精细化工, 化工产品, 核心技术, 智慧化工园区, 军民融合, 天津锐兵能源">

    <!-- 引入 Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- 引入字体库 (国内镜像) -->
    <link rel="preconnect" href="https://fonts.googleapis.cn">
    <link rel="preconnect" href="https://fonts.gstatic.cn" crossorigin>
    <link
        href="https://fonts.googleapis.cn/css2?family=Inter:wght@400;500;600;700&family=Noto+Sans+SC:wght@400;500;700&display=swap"
        rel="stylesheet">

    <!-- 引入图标库 (Lucide Icons) -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        sans: ['Inter', 'Noto Sans SC', 'sans-serif'],
                    },
                    colors: {
                        'brand-blue': {
                            '900': '#0A2D57', // 深蓝
                            '800': '#0D3A6F',
                            '600': '#1A5A9C',
                            '500': '#2A7BC8',
                            '100': '#E6F0FA',
                        },
                        'brand-gray': {
                            '100': '#f3f4f6',
                            '200': '#e5e7eb',
                            '500': '#6b7280',
                            '800': '#1f2937',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        html {
            scroll-behavior: smooth;
        }

        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        section {
            scroll-margin-top: 80px;
        }

        /* 导航栏高度 */
        .backdrop-blur-custom {
            -webkit-backdrop-filter: blur(8px);
            backdrop-filter: blur(8px);
        }

        .cta-button {
            transition: all 0.3s ease;
        }

        .gateway-card {
            transition: all 0.3s ease;
        }

        .gateway-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
        }

        .case-card img {
            transition: transform 0.3s ease;
        }

        .case-card:hover img {
            transform: scale(1.05);
        }

        /* 瀑布流布局样式 */
        .masonry-grid {
            column-count: 1;
            column-gap: 1.5rem;
            /* 24px */
        }

        @media (min-width: 768px) {
            .masonry-grid {
                column-count: 2;
            }
        }

        .masonry-item {
            break-inside: avoid;
            margin-bottom: 1.5rem;
            /* 24px */
        }

        /* 专家头像 */
        .expert-avatar {
            width: 128px;
            height: 128px;
            object-fit: cover;
            border: 4px solid white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* 模态框动画 */
        .modal-enter-active,
        .modal-leave-active {
            transition: opacity 0.3s ease;
        }

        .modal-enter-from,
        .modal-leave-to {
            opacity: 0;
        }

        .modal-content-enter-active,
        .modal-content-leave-active {
            transition: all 0.3s ease;
        }

        .modal-content-enter-from,
        .modal-content-leave-to {
            opacity: 0;
            transform: translateY(-20px);
        }

        /* 专家滚动动画 */
        .scroller-wrapper {
            overflow: hidden;
            -webkit-mask: linear-gradient(90deg, transparent, white 20%, white 80%, transparent);
            mask: linear-gradient(90deg, transparent, white 20%, white 80%, transparent);
        }

        .scroller-inner {
            display: flex;
            gap: 2.5rem;
            /* 40px */
            width: max-content;
            animation: scroll 40s linear infinite;
        }

        .scroller-wrapper:hover .scroller-inner {
            animation-play-state: paused;
        }

        .expert-card-item {
            width: 300px;
            flex-shrink: 0;
        }

        @keyframes scroll {
            to {
                transform: translateX(calc(-50% - 1.25rem));
                /* (宽度的一半) + (gap的一半) */
            }
        }
    </style>
    <script>
        var _hmt = _hmt || [];
        (function () {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?a7354fdb293a8af7eb873d3501ed6595";
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>

</head>

<body class="bg-white text-brand-gray-800 font-sans">

    <!-- 全局页眉 -->
    <header id="header"
        class="bg-white/80 backdrop-blur-custom shadow-md fixed w-full z-50 top-0 transition-all duration-300">
        <div class="container mx-auto px-6 py-3 flex justify-between items-center">
            <a href="#home" class="flex items-center space-x-3">
                <img src="./images/logo.png" alt="天津锐兵能源科技 Logo" class="h-12 w-auto"
                    onerror="this.onerror=null;this.src='https://placehold.co/200x100?text=Logo';">
            </a>
            <!-- 1. 新的导航栏顺序 -->
            <nav class="hidden lg:flex space-x-6">
                <a href="#home"
                    class="text-brand-gray-800 hover:text-brand-blue-500 transition duration-300 font-medium">首页</a>
                <a href="#products"
                    class="text-brand-gray-800 hover:text-brand-blue-500 transition duration-300 font-medium">产品中心</a>
                <a href="#services"
                    class="text-brand-gray-800 hover:text-brand-blue-500 transition duration-300 font-medium">核心技术与服务</a>
                <a href="#solutions"
                    class="text-brand-gray-800 hover:text-brand-blue-500 transition duration-300 font-medium">智慧园区解决方案</a>
                <a href="#cases"
                    class="text-brand-gray-800 hover:text-brand-blue-500 transition duration-300 font-medium">成功案例</a>
                <a href="#about"
                    class="text-brand-gray-800 hover:text-brand-blue-500 transition duration-300 font-medium">关于我们</a>
            </nav>
            <div class="flex items-center">
                <a href="#contact"
                    class="hidden sm:inline-block bg-brand-blue-600 hover:bg-brand-blue-800 text-white font-bold py-2 px-5 rounded-full cta-button">联系我们</a>
                <button id="mobile-menu-button"
                    class="lg:hidden ml-4 p-2 rounded-md text-brand-gray-800 hover:bg-gray-100">
                    <span class="sr-only">打开主菜单</span>
                    <svg id="menu-open-icon" class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                    <svg id="menu-close-icon" class="h-6 w-6 hidden" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>
        <!-- 移动端导航 -->
        <div id="mobile-menu" class="hidden lg:hidden bg-white/95 backdrop-blur-custom">
            <a href="#home" class="block py-3 px-6 text-center text-brand-gray-800 hover:bg-brand-gray-100">首页</a>
            <a href="#products" class="block py-3 px-6 text-center text-brand-gray-800 hover:bg-brand-gray-100">产品中心</a>
            <a href="#services"
                class="block py-3 px-6 text-center text-brand-gray-800 hover:bg-brand-gray-100">核心技术与服务</a>
            <a href="#solutions"
                class="block py-3 px-6 text-center text-brand-gray-800 hover:bg-brand-gray-100">智慧园区解决方案</a>
            <a href="#cases" class="block py-3 px-6 text-center text-brand-gray-800 hover:bg-brand-gray-100">成功案例</a>
            <a href="#about" class="block py-3 px-6 text-center text-brand-gray-800 hover:bg-brand-gray-100">关于我们</a>
            <a href="#contact" class="block py-3 px-6 text-center text-brand-gray-800 hover:bg-brand-gray-100">联系我们</a>
        </div>
    </header>

    <main>
        <!-- I. 首页 -->
        <section id="home" class="pt-20">
            <!-- 1.1 品牌形象区 (Hero Section) 内容更新 -->
            <div class="relative h-[calc(100vh-80px)] min-h-[600px] flex items-center justify-center text-white text-center bg-cover bg-center"
                style="background-image: url('https://storage.googleapis.com/gemini-assets/static/images/ruibing_hero_bg.jpg');">
                <div class="absolute inset-0 bg-brand-blue-900 bg-opacity-60"></div>
                <div class="relative z-10 p-6 max-w-4xl mx-auto">
                    <h1 class="text-4xl md:text-6xl font-bold mb-4 leading-tight">全球化工资源整合者，产业升级价值伙伴</h1>
                    <p class="text-lg md:text-xl mb-8">天津锐兵能源科技，依托中国兵器集团军民联合企业与全球供应链，为您提供高品质化工产品贸易、化工工程设计、全链条技术服务与领先的智慧化工园区解决方案。
                    </p>
                    <div class="flex flex-col sm:flex-row justify-center gap-4">
                        <a href="#products"
                            class="bg-brand-blue-500 hover:bg-brand-blue-600 text-white font-bold py-3 px-8 rounded-full text-lg cta-button">进入产品中心</a>
                        <a href="#services"
                            class="bg-gray-700/50 hover:bg-gray-700 text-white font-bold py-3 px-8 rounded-full text-lg cta-button">了解技术服务</a>
                    </div>
                </div>
            </div>

                    <!-- VI. 关于我们 -->
        <section id="about" class="py-20 bg-brand-gray-100">
            <div class="container mx-auto px-6">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-brand-blue-900">关于我们</h2>
                    <p class="text-brand-gray-500 mt-2">锐兵的故事、基因与智库</p>
                    <div class="inline-block w-24 h-1 bg-brand-blue-500 rounded mt-4"></div>
                </div>

                <!-- 我们的DNA -->
                <div class="relative rounded-lg overflow-hidden shadow-2xl mb-16 min-h-[450px] flex items-center">
                    <img src="./images/08-dna-background.jpg" alt="军民融合"
                        class="w-full h-full object-cover absolute inset-0">
                    <div
                        class="absolute inset-0 bg-gradient-to-r from-brand-blue-900/80 via-brand-blue-900/50 to-transparent">
                    </div>
                    <div class="relative z-10 p-8 md:p-16 text-white w-full">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                            <div class="text-center md:text-left">
                                <h3 class="text-4xl lg:text-5xl font-bold">我们的<span
                                        class="text-blue-400">DNA</span>：<br>军民融合</h3>
                            </div>
                            <div class="border-t-2 md:border-t-0 md:border-l-2 border-blue-400/50 pt-6 md:pt-0 md:pl-8">
                                <blockquote class="max-w-2xl">
                                    <p class="text-lg md:text-xl leading-relaxed">
                                        “可靠性不是一种选择，而是一种纪律。我们传承中国兵器集团的严谨、精准与担当，将‘使命必达’的军工精神，熔铸于每一次技术攻关与项目交付之中。”</p>
                                </blockquote>
                                <p class="mt-4 text-sm text-gray-300">— 源自中国兵器集团的企业基因</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 锐兵军民联合企业 -->
                <div class="mb-16">
                    <h3 class="text-2xl font-bold text-brand-blue-800 mb-8 text-center">锐兵军民联合企业</h3>
                    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                        <!-- 企业一 -->
                        <div class="bg-white rounded-lg shadow-lg overflow-hidden group transform hover:-translate-y-2 transition-transform duration-300">
                            <div class="aspect-video w-full overflow-hidden">
                                <img src="./images/bingqi-hubeidongfang.jpg" alt="中国兵器集团湖北东方化工有限公司" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                            </div>
                            <div class="p-6">
                                <h4 class="font-bold text-lg text-brand-blue-900 mb-2">中国兵器集团湖北东方化工有限公司</h4>
                                <p class="text-sm text-brand-gray-600"><b>合作领域：</b>合作生产对硝基甲苯、邻硝基甲苯及下游新产品开发。</p>
                            </div>
                        </div>
                        <!-- 企业二 -->
                        <div class="bg-white rounded-lg shadow-lg overflow-hidden group transform hover:-translate-y-2 transition-transform duration-300">
                            <div class="aspect-video w-full overflow-hidden">
                                <img src="./images/bingqi-gansuyinguang.jpg" alt="中国兵器集团甘肃银光化学工业集团" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                            </div>
                            <div class="p-6">
                                <h4 class="font-bold text-lg text-brand-blue-900 mb-2">中国兵器集团甘肃银光化学工业集团</h4>
                                <p class="text-sm text-brand-gray-600"><b>合作产品：</b>2,4-二硝基甲苯、2,6-二硝基甲苯。</p>
                            </div>
                        </div>
                        <!-- 企业三 -->
                        <div class="bg-white rounded-lg shadow-lg overflow-hidden group transform hover:-translate-y-2 transition-transform duration-300">
                            <div class="aspect-video w-full overflow-hidden">
                                <img src="./images/bingqi-sichuanbeifang.jpg" alt="中国兵器集团四川红光特种化工有限公司" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                            </div>
                            <div class="p-6">
                                <h4 class="font-bold text-lg text-brand-blue-900 mb-2">中国兵器集团四川红光特种化工有限公司</h4>
                                <p class="text-sm text-brand-gray-600"><b>合作领域：</b>合作生产研发4-硝基邻二甲苯、3-硝基邻二甲苯、对苯二胺、间苯二胺。</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 我们的生态 (整合了专家团队) -->
                <div class="bg-white p-8 md:p-12 rounded-xl shadow-lg">
                    <h3 class="text-2xl font-bold text-brand-blue-800 mb-4 text-center">我们的生态：产学研智库联盟</h3>
                    <p class="text-lg text-gray-700 leading-relaxed text-center max-w-3xl mx-auto mb-8">
                        真正的创新并非单打独斗，而是生态共荣。我们构建了一个由顶尖科学家、数字架构师和资深工程师组成的强大智库联盟，实现了从‘理论-工程-智能’的无缝衔接，他们是驱动我们技术服务与解决方案的核心力量。
                    </p>
                    <div class="overflow-x-auto bg-white rounded-lg border">
                        <table class="w-full text-left">
                            <thead class="bg-brand-blue-800 text-white">
                                <tr>
                                    <th class="p-4 font-semibold">核心能力领域</th>
                                    <th class="p-4 font-semibold">领军专家/机构</th>
                                    <th class="p-4 font-semibold">关键资历与贡献</th>
                                </tr>
                            </thead>
                            <tbody class="text-brand-gray-800">
                                <tr class="bg-gray-50 border-b">
                                    <td class="p-4 align-top"><strong>化工工艺源头创新</strong></td>
                                    <td class="p-4 align-top">王红星 教授 / 天津科技大学</td>
                                    <td class="p-4 align-top text-sm">
                                        天津市创新人才推进计划领军人才，精馏与反应精馏技术专家。主持横向科研项目60余项，授权国内发明专利75项。</td>
                                </tr>
                                <tr class="border-b">
                                    <td class="p-4 align-top"><strong>工业化工程落地</strong></td>
                                    <td class="p-4 align-top">岳庆彬 / 山东富海石化工程</td>
                                    <td class="p-4 align-top text-sm">资深工程项目总经理，原中国天辰工程有限公司核心成员。具备甲级工程设计资质和丰富的EPC管理经验。
                                    </td>
                                </tr>
                                <tr class="bg-gray-50">
                                    <td class="p-4 align-top"><strong>新药研发与连续流技术</strong></td>
                                    <td class="p-4 align-top">赵庆杰 / 特聘青年研究员</td>
                                    <td class="p-4 align-top text-sm">
                                        上海市“浦江人才”，专注于新药发现与连续流技术应用，发表SCI论文30余篇，主持多个国家级和中科院重大项目。</td>
                                </tr>
                                <tr class="border-b">
                                    <td class="p-4 align-top"><strong>智慧平台顶层设计</strong></td>
                                    <td class="p-4 align-top">任跃青</td>
                                    <td class="p-4 align-top text-sm">
                                        江苏省“创新人才”，专注于能源大数据/工业互联网/AI人工智能/数字孪生技术应用，“1+4+1”智慧园区总设计师，主导设计交付多个国家级、行业级大型智慧平台。
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- NEW: 核心专家团队 -->
                    <div class="mt-16">
                        <h3 class="text-2xl font-bold text-brand-blue-800 mb-8 text-center">核心专家团队</h3>
                        <div class="scroller-wrapper">
                            <div id="expert-cards-container" class="scroller-inner">
                                <!-- 专家卡片将由JS动态生成, 两遍 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

            <!-- 1.2 核心业务网关 (Gateway Section) 顺序与内容调整 -->
            <div class="py-20 bg-white">
                <div class="container mx-auto px-6">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl md:text-4xl font-bold text-brand-blue-900">我们的核心业务</h2>
                    </div>
                    <!-- 卡片顺序: 产品中心 -> 核心技术 -> 工程设计 -> 智慧园区 -->
                    <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                        <!-- 卡片一: 产品中心 -->
                        <div class="gateway-card bg-brand-gray-100 p-8 rounded-xl shadow-lg group flex flex-col">
                            <div class="aspect-[16/9] w-full overflow-hidden rounded-lg mb-6">
                                <img src="./images/01-product-center.jpg" alt="产品中心"
                                    class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105">
                            </div>
                            <h3 class="text-2xl font-bold text-brand-blue-900 mb-3">产品中心 (贸易)</h3>
                            <p class="text-brand-gray-500 mb-6 flex-grow">
                                供应高品质化工产品与中间体。我们不仅是生产商，更是全球化工资源的整合者，凭借强大的供应链网络和对品质的严苛把控，为全球客户提供稳定、可靠的化工产品。</p>
                            <a href="#products" class="font-bold text-brand-blue-600 hover:underline mt-auto">浏览我们的产品 &rarr;</a>
                        </div>
                        <!-- 卡片二: 核心技术 -->
                        <div class="gateway-card bg-brand-gray-100 p-8 rounded-xl shadow-lg group flex flex-col">
                            <div class="aspect-[16/9] w-full overflow-hidden rounded-lg mb-6">
                                <img src="./images/02-core-technology.jpg" alt="核心技术与服务"
                                    class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105">
                            </div>
                            <h3 class="text-2xl font-bold text-brand-blue-900 mb-3">核心技术与服务</h3>
                            <p class="text-brand-gray-500 mb-6 flex-grow">依托产学研联盟，提供从实验室研发、中试放大到工业化设计的全链条技术服务，攻克工艺难题，加速成果转化。</p>
                            <a href="#services" class="font-bold text-brand-blue-600 hover:underline mt-auto">探索我们的技术 &rarr;</a>
                        </div>
                        <!-- NEW CARD: 工程设计与咨询 -->
                        <div class="gateway-card bg-brand-gray-100 p-8 rounded-xl shadow-lg group flex flex-col">
                            <div class="aspect-[16/9] w-full overflow-hidden rounded-lg mb-6">
                                <img src="./images/22-engineering-design.jpg" alt="工程设计与咨询"
                                    class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105">
                            </div>
                            <h3 class="text-2xl font-bold text-brand-blue-900 mb-3">工程设计与咨询</h3>
                            <p class="text-brand-gray-500 mb-6 flex-grow">由资深专家领衔，提供化工、石化、医药行业EPC总承包管理服务，将创新技术与蓝图可靠地转化为高标准、高效率的现实工厂。</p>
                            <a href="#services" class="font-bold text-brand-blue-600 hover:underline mt-auto">了解工程能力 &rarr;</a>
                        </div>
                        <!-- 卡片四: 智慧园区 -->
                        <div class="gateway-card bg-brand-gray-100 p-8 rounded-xl shadow-lg group flex flex-col">
                            <div class="aspect-[16/9] w-full overflow-hidden rounded-lg mb-6">
                                <img src="./images/03-smart-park.jpg" alt="智慧化工园区解决方案"
                                    class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105">
                            </div>
                            <h3 class="text-2xl font-bold text-brand-blue-900 mb-3">智慧化工园区</h3>
                            <p class="text-brand-gray-500 mb-6 flex-grow">应对安全、环保、效能挑战，我们以“1+4+1”蓝图构建新一代智慧化工园区，实现本质安全与可持续发展。</p>
                            <a href="#solutions" class="font-bold text-brand-blue-600 hover:underline mt-auto">查看解决方案 &rarr;</a>
                        </div>
                    </div>

                </div>
            </div>

            <!-- 1.3 信誉快照 -->
            <div class="py-20 bg-brand-blue-900 text-white">
                <div class="container mx-auto px-6">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl md:text-4xl font-bold">为何信赖锐兵</h2>
                    </div>
                    <div class="grid md:grid-cols-3 gap-10 text-center">
                        <div>
                            <i data-lucide="shield" class="h-12 w-12 mx-auto mb-4 text-brand-blue-500"></i>
                            <h4 class="text-xl font-bold mb-2">军民融合，卓越品质</h4>
                            <p class="text-gray-300">源自中国兵器集团，我们将军事工业的严苛标准与可靠性注入每一个解决方案与产品。</p>
                        </div>
                        <div>
                            <i data-lucide="globe-2" class="h-12 w-12 mx-auto mb-4 text-brand-blue-500"></i>
                            <h4 class="text-xl font-bold mb-2">全球视野，强大供应</h4>
                            <p class="text-gray-300">整合全球化工资源，构建高效稳定的供应链网络，确保为客户提供最具竞争力的产品和服务。</p>
                        </div>
                        <div>
                            <i data-lucide="network" class="h-12 w-12 mx-auto mb-4 text-brand-blue-500"></i>
                            <h4 class="text-xl font-bold mb-2">产学研联盟，顶尖智库</h4>
                            <p class="text-gray-300">联合顶尖机构与行业领军专家，构建从源头创新到产业落地的强大生态，驱动产业持续升级。</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- II. 产品中心 -->
        <section id="products" class="py-20 bg-white">
            <div class="container mx-auto px-6">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-brand-blue-900">产品中心</h2>
                    <p class="text-brand-gray-500 mt-2">高品质精细化工品的全球贸易专家</p>
                    <div class="inline-block w-24 h-1 bg-brand-blue-500 rounded mt-4"></div>
                </div>
                <!-- 增加引言 -->
                <p class="max-w-3xl mx-auto text-center text-lg text-brand-gray-800 mb-12">
                    我们专注于高品质精细化工品的全球贸易。凭借与国内外顶尖制造商的深度合作以及我们自身强大的研发与品控能力，我们确保向客户交付的每一批产品都符合最严苛的行业标准。
                </p>
                <div class="mb-8 max-w-lg mx-auto">
                    <div class="relative">
                        <input type="text" id="productSearch" placeholder="搜索产品名称、英文名或CAS号..."
                            class="w-full px-4 py-3 border border-brand-gray-200 rounded-full focus:ring-2 focus:ring-brand-blue-500 focus:outline-none">
                        <div class="absolute right-4 top-1/2 -translate-y-1/2 text-brand-gray-500"><i
                                data-lucide="search" class="h-5 w-5"></i></div>
                    </div>
                </div>
                <div class="overflow-x-auto bg-white rounded-lg shadow-lg">
                    <table id="productTable" class="w-full text-left table-auto">
                        <thead class="bg-brand-blue-900 text-white">
                            <tr>
                                <th class="px-6 py-4 font-semibold">产品中文名</th>
                                <th class="px-6 py-4 font-semibold hidden md:table-cell">产品英文名</th>
                                <th class="px-6 py-4 font-semibold">CAS号</th>
                                <th class="px-6 py-4 font-semibold hidden sm:table-cell">分子式</th>
                            </tr>
                        </thead>
                        <tbody class="text-brand-gray-800">
                            <!-- JS will populate this -->
                        </tbody>
                    </table>
                </div>
            </div>
        </section>

        <!-- III. 核心技术与服务 -->
        <section id="services" class="py-20 bg-brand-gray-100">
            <div class="container mx-auto px-6">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-brand-blue-900">核心技术与服务</h2>
                    <p class="text-brand-gray-500 mt-2">从源头创新到数字化运营的全生命周期价值链</p>
                    <div class="inline-block w-24 h-1 bg-brand-blue-500 rounded mt-4"></div>
                </div>
                <div class="grid md:grid-cols-2 gap-8">
                    <div class="bg-white p-8 rounded-lg shadow-md">
                        <i data-lucide="flask-conical" class="h-10 w-10 text-brand-blue-600 mb-4"></i>
                        <h3 class="text-xl font-bold mb-3">化工工艺开发：创新的源头</h3>
                        <p class="text-gray-600">
                            与王红星教授等顶尖学术力量合作，专注精馏、反应精馏等前沿研究，开发电子级化学品提纯、节能新工艺。我们能将过程数字化，因为我们深度参与创造了过程本身。</p>
                    </div>
                    <div class="bg-white p-8 rounded-lg shadow-md">
                        <i data-lucide="scaling" class="h-10 w-10 text-brand-blue-600 mb-4"></i>
                        <h3 class="text-xl font-bold mb-3">研发与中试：连接理论与现实</h3>
                        <p class="text-gray-600">拥有先进的实验室与中试基地，提供从小试、中试到工艺包（PDP）编制的全流程服务，将创新的工艺理念转化为可行的工业化方案。</p>
                    </div>
                    <div class="bg-white p-8 rounded-lg shadow-md">
                        <i data-lucide="hard-hat" class="h-10 w-10 text-brand-blue-600 mb-4"></i>
                        <h3 class="text-xl font-bold mb-3">工程设计与咨询：构筑未来实体</h3>
                        <p class="text-gray-600">由资深工程管理专家岳庆彬先生领衔，提供化工、石化、医药行业EPC总承包管理，确保创新技术可靠落地。</p>
                    </div>
                    <div class="bg-white p-8 rounded-lg shadow-md">
                        <i data-lucide="zap" class="h-10 w-10 text-brand-blue-600 mb-4"></i>
                        <h3 class="text-xl font-bold mb-3">技术成果转化：价值的催化剂</h3>
                        <p class="text-gray-600">我们不仅是技术开发者，更是价值放大器。凭借对工艺、工程、市场的综合理解，帮助技术对接产业资源，完成从“0到1”的突破。</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- IV. 智慧园区解决方案 -->
        <section id="solutions" class="py-20 bg-white">
            <div class="container mx-auto px-6">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-brand-blue-900">智慧园区解决方案</h2>
                    <p class="text-brand-gray-500 mt-2">构建“安全、环保、智能、高效、低碳”的新一代智慧化工园区</p>
                    <div class="inline-block w-24 h-1 bg-brand-blue-500 rounded mt-4"></div>
                </div>

                <!-- “1+4+1”蓝图 -->
                <div class="bg-brand-blue-100 p-8 rounded-2xl">
                    <h3 class="text-2xl font-bold text-brand-blue-900 text-center mb-8">“1+4+1” 建设蓝图</h3>
                    <div class="flex flex-col gap-6">
                        <!-- 1个目标 -->
                        <div class="text-center p-4 bg-brand-blue-500 text-white rounded-lg">
                            <h4 class="font-bold text-xl">1个目标：打造先进的智慧化工园标杆</h4>
                        </div>

                        <!-- 4大核心能力 -->
                        <div class="bg-white rounded-xl shadow-lg p-8">
                            <div class="text-center mb-8">
                                <h4 class="inline-block text-xl font-bold text-brand-blue-900 pb-2 border-b-2 border-brand-blue-500">四大体系</h4>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                <div class="p-6 bg-brand-gray-100 rounded-lg h-full">
                                    <i data-lucide="line-chart" class="h-8 w-8 text-brand-blue-600 mb-3"></i>
                                    <h5 class="font-bold text-lg mb-2">产业发展数字化</h5>
                                    <p class="text-sm text-gray-600">通过“园区产业大脑”实现精准招商、产业链协同和产业治理，推动园区高质量发展。</p>
                                </div>
                                <div class="p-6 bg-brand-gray-100 rounded-lg h-full">
                                    <i data-lucide="shield-check" class="h-8 w-8 text-brand-blue-600 mb-3"></i>
                                    <h5 class="font-bold text-lg mb-2">运营管理智能化</h5>
                                    <p class="text-sm text-gray-600">提供安全基础管理、重大危险源管理、双重预防机制等核心功能，实现本质安全。</p>
                                </div>
                                <div class="p-6 bg-brand-gray-100 rounded-lg h-full">
                                    <i data-lucide="leaf" class="h-8 w-8 text-brand-blue-600 mb-3"></i>
                                    <h5 class="font-bold text-lg mb-2">园区环境绿色化</h5>
                                    <p class="text-sm text-gray-600">通过“能碳双控”模块，对园区能碳数据全面梳理与管控，打造绿色低碳园区。</p>
                                </div>
                                <div class="p-6 bg-brand-gray-100 rounded-lg h-full">
                                    <i data-lucide="users" class="h-8 w-8 text-brand-blue-600 mb-3"></i>
                                    <h5 class="font-bold text-lg mb-2">园区服务人文化</h5>
                                    <p class="text-sm text-gray-600">打造面向企业及员工的“多元、高效、便捷、共享”的一站式服务平台。</p>
                                </div>
                            </div>
                        </div>

                        <!-- 1个平台 -->
                        <div class="p-6 bg-brand-blue-500 text-white rounded-lg text-center">
                            <i data-lucide="database-zap" class="h-8 w-8 text-white mb-3 mx-auto"></i>
                            <h5 class="font-bold text-lg mb-2">1个平台：园区智慧统一数字平台</h5>
                            <p class="text-sm text-gray-300">基于云原生、微服务、大数据和物联网技术，构建坚实可靠的数字化底座，支撑上层所有智慧应用。</p>
                        </div>
                    </div>
                </div>

                <!-- IOC智慧运营管控中心 -->
                <div class="mt-16">
                    <h3 class="text-2xl font-bold text-brand-blue-900 text-center mb-8">IOC智慧运营管控中心：全局可视与智能决策</h3>
                    <div class="masonry-grid">
                        <div class="masonry-item"><img src="./images/04-cockpit.jpg" alt="园区数孪综合驾驶舱"
                                class="rounded-lg shadow-xl w-full h-auto object-cover"></div>
                        <div class="masonry-item"><img src="./images/05-park-theme.jpg" alt="园区专题展示"
                                class="rounded-lg shadow-xl w-full h-auto object-cover"></div>
                        <div class="masonry-item"><img src="./images/06-multi-dimension.jpg" alt="园区运行多维体征"
                                class="rounded-lg shadow-xl w-full h-auto object-cover"></div>
                        <div class="masonry-item"><img src="./images/07-control-center.jpg" alt="集控指挥调度"
                                class="rounded-lg shadow-xl w-full h-auto object-cover"></div>
                    </div>
                    <p class="text-center mt-6 text-gray-600">
                        通过构建园区级数字孪生体，实现对人、车、物、设备、环境等全要素的虚实映射与动态仿真，提供前所未有的全局洞察与协同指挥能力。</p>
                </div>
            </div>
        </section>

        <!-- V. 成功案例 -->
        <section id="cases" class="py-20 bg-brand-gray-100">
            <div class="container mx-auto px-6">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-brand-blue-900">成功案例</h2>
                    <p class="text-brand-gray-500 mt-2">实践是检验能力的唯一标准</p>
                    <div class="inline-block w-24 h-1 bg-brand-blue-500 rounded mt-4"></div>
                </div>
                <div class="grid md:grid-cols-2 gap-8">
                    <div class="case-card bg-white rounded-lg shadow-lg overflow-hidden group">
                        <div class="aspect-video w-full overflow-hidden">
                            <img src="./images/09-case-chemical-park.jpg" alt="国家级化工园区本质安全提升项目"
                                class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105">
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-2">国家级化工园区本质安全提升项目</h3>
                            <p class="text-gray-600"><b>聚焦:</b> 本质安全提升与绿色环保监管。重点展示重大危险源监控、双重预防机制和水、气、土环境在线监测等功能的实际应用成效。
                            </p>
                        </div>
                    </div>
                    <div class="case-card bg-white rounded-lg shadow-lg overflow-hidden group">
                        <div class="aspect-video w-full overflow-hidden">
                            <img src="./images/10-case-manufacturing.jpg" alt="百亿级制造企业集团智慧运营管控项目"
                                class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105">
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-2">百亿级制造企业集团智慧运营管控项目</h3>
                            <p class="text-gray-600"><b>聚焦:</b> 大型制造集团的多基地统一运营管理。突出展示平台如何整合26个子系统，实现集团级的集中管控、业务联动和能耗管理。
                            </p>
                        </div>
                    </div>
                    <div class="case-card bg-white rounded-lg shadow-lg overflow-hidden group">
                        <div class="aspect-video w-full overflow-hidden">
                            <img src="./images/11-case-industrial-brain.jpg" alt="国家级经开区产业大脑决策支持平台"
                                class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105">
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-2">国家级经开区产业大脑决策支持平台</h3>
                            <p class="text-gray-600"><b>聚焦:</b> 产业园区的大数据治理与政府决策支持。展示产业看板、招商管理和政企直通车等功能，辅助政府进行产业分析和精准施策。
                            </p>
                        </div>
                    </div>
                    <div class="case-card bg-white rounded-lg shadow-lg overflow-hidden group">
                        <div class="aspect-video w-full overflow-hidden">
                            <img src="./images/12-case-smart-office.jpg" alt="省级政府机关智慧后勤与节能管理平台"
                                class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105">
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-2">省级政府机关智慧后勤与节能管理平台</h3>
                            <p class="text-gray-600"><b>聚焦:</b> 解决方案的跨行业通用性。展示平台在智慧停车、智慧消防和绿色建筑节能管理的应用，证明了技术底座的普适性。</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>



        <!-- VII. 联系我们 -->
        <section id="contact" class="py-20 bg-brand-blue-100">
            <div class="container mx-auto px-6">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-brand-blue-900">联系我们</h2>
                    <p class="text-brand-gray-500 mt-2">期待与您携手，共创未来</p>
                    <div class="inline-block w-24 h-1 bg-brand-blue-500 rounded mt-4"></div>
                </div>
                <div class="bg-white p-8 md:p-12 rounded-xl shadow-lg grid md:grid-cols-5 gap-12">
                    <div class="md:col-span-2 space-y-6">
                        <div class="flex items-start space-x-4">
                            <i data-lucide="map-pin" class="h-6 w-6 text-brand-blue-500 mt-1 flex-shrink-0"></i>
                            <div>
                                <h4 class="font-semibold">公司地址</h4>
                                <p class="text-brand-gray-500">天津市滨海新区滨海琴墅天琴轩4-7</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-4">
                            <i data-lucide="phone" class="h-6 w-6 text-brand-blue-500 mt-1 flex-shrink-0"></i>
                            <div>
                                <h4 class="font-semibold">联系电话</h4>
                                <a href="tel:13116157777"
                                    class="text-brand-gray-500 hover:text-brand-blue-500">131-1615-7777</a>
                            </div>
                        </div>
                        <div class="w-full h-64 md:h-80 mt-6 rounded-lg overflow-hidden">
                            <iframe width="100%" height="100%" frameborder="0" scrolling="no" style="border:0"
                                src="//api.map.baidu.com/marker?location=39.04332,117.653606&title=天津锐兵能源科技有限公司&content=天津市滨海新区滨海琴墅天琴轩4-7&output=html&src=webapp.rebie.ruibing"></iframe>
                        </div>
                    </div>
                    <div class="md:col-span-3">
                        <h3 class="text-2xl font-bold text-brand-blue-900 mb-4">在线咨询</h3>
                        <form action="#" method="POST">
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700">您的姓名</label>
                                    <input type="text" name="name" id="name"
                                        class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-brand-blue-500 focus:border-brand-blue-500">
                                </div>
                                <div>
                                    <label for="company" class="block text-sm font-medium text-gray-700">公司名称</label>
                                    <input type="text" name="company" id="company"
                                        class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-brand-blue-500 focus:border-brand-blue-500">
                                </div>
                            </div>
                            <div class="mt-4">
                                <label for="interest" class="block text-sm font-medium text-gray-700">我感兴趣的是...</label>
                                <select id="interest" name="interest"
                                    class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-brand-blue-500 focus:border-brand-blue-500 sm:text-sm rounded-md">
                                    <option>产品购买询价</option>
                                    <option>技术服务咨询</option>
                                    <option>智慧园区合作</option>
                                    <option>其他事宜</option>
                                </select>
                            </div>
                            <div class="mt-4">
                                <label for="message" class="block text-sm font-medium text-gray-700">留言内容</label>
                                <textarea id="message" name="message" rows="4"
                                    class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-brand-blue-500 focus:border-brand-blue-500"></textarea>
                            </div>
                            <div class="mt-6">
                                <button type="submit"
                                    class="w-full bg-brand-blue-600 hover:bg-brand-blue-800 text-white font-bold py-3 px-6 rounded-lg cta-button">提交信息</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 全局页脚 -->
    <footer class="bg-brand-blue-900 text-white">
        <div class="container mx-auto px-6 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h4 class="font-bold text-lg mb-4">天津锐兵能源科技</h4>
                    <p class="text-sm text-gray-300">全球化工资源整合者，产业升级价值伙伴。</p>
                </div>
                <div>
                    <h4 class="font-bold text-lg mb-4">快速导航</h4>
                    <ul class="space-y-2 text-sm">
                        <li><a href="#products" class="text-gray-300 hover:text-white">产品中心</a></li>
                        <li><a href="#services" class="text-gray-300 hover:text-white">核心技术与服务</a></li>
                        <li><a href="#solutions" class="text-gray-300 hover:text-white">智慧园区解决方案</a></li>
                        <li><a href="#cases" class="text-gray-300 hover:text-white">成功案例</a></li>
                        <li><a href="#about" class="text-gray-300 hover:text-white">关于我们</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-bold text-lg mb-4">联系我们</h4>
                    <ul class="space-y-2 text-sm text-gray-300">
                        <li class="flex items-start"><i data-lucide="map-pin"
                                class="h-4 w-4 mr-2 mt-1 flex-shrink-0"></i><span>天津市滨海新区滨海琴墅天琴轩4-7</span></li>
                        <li class="flex items-start"><i data-lucide="phone"
                                class="h-4 w-4 mr-2 mt-1 flex-shrink-0"></i><span>131-1615-7777</span></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-bold text-lg mb-4">战略合作伙伴</h4>
                    <div class="flex flex-wrap gap-4">
                        <img src="./images/13-partner-norinco.jpg" alt="中国兵器集团" class="h-8 bg-white p-1 rounded">
                        <img src="./images/14-partner-cas.jpg" alt="中科院生物医药研究所" class="h-8 bg-white p-1 rounded">
                        <img src="./images/15-partner-tju.jpg" alt="天津大学" class="h-8 bg-white p-1 rounded">
                        <img src="./images/16-partner-tust.jpg" alt="天津科技大学" class="h-8 bg-white p-1 rounded">
                        <img src="./images/17-partner-fuhai.jpg" alt="富海石化设计院" class="h-8 bg-white p-1 rounded">
                    </div>
                </div>
            </div>
            <div class="mt-8 border-t border-gray-700 pt-8 text-center text-sm text-gray-400">
                <p>&copy; 2025 天津锐兵能源科技有限公司. All Rights Reserved. </p>
            </div>
        </div>
    </footer>

    <!-- 专家详情模态框 -->
    <div id="expert-modal"
        class="fixed inset-0 bg-black bg-opacity-60 z-50 flex items-center justify-center p-4 hidden modal-enter-from">
        <div id="modal-content"
            class="bg-white rounded-lg shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-y-auto modal-content-enter-from">
            <!-- 内容将由JS动态填充 -->
        </div>
    </div>


    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // 激活 Lucide 图标
            lucide.createIcons();

            // 产品数据及搜索功能
            const products = [
                { name: '2,4-二硝基甲苯', enName: '2,4-Dinitrotoluene', cas: '121-14-2', formula: 'C₇H₆N₂O₄' },
                { name: '2,6-二硝基甲苯', enName: '2,6-Dinitrotoluene', cas: '606-20-2', formula: 'C₇H₆N₂O₄' },
                { name: '3,4-二硝基甲苯', enName: '3,4-Dinitrotoluene', cas: '610-39-9', formula: 'C₇H₆N₂O₄' },
                { name: '2,5-二硝基甲苯', enName: '2,5-Dinitrotoluene', cas: '619-15-8', formula: 'C₇H₆N₂O₄' },
                { name: '间二硝基苯', enName: '1,3-Dinitrobenzene', cas: '99-65-0', formula: 'C₆H₄N₂O₄' },
                { name: '4-硝基邻二甲苯', enName: '4-Nitro-o-xylene', cas: '99-51-4', formula: 'C₈H₉NO₂' },
                { name: '3-硝基邻二甲苯', enName: '3-Nitro-o-xylene', cas: '83-41-0', formula: 'C₈H₉NO₂' },
                { name: '3,5-二硝基苯甲酸', enName: '3,5-Dinitrobenzoic acid', cas: '99-34-3', formula: 'C₇H₄N₂O₆' },
                { name: '对硝基苯甲酸', enName: '4-Nitrobenzoic acid', cas: '62-23-7', formula: 'C₇H₅NO₄' },
                { name: '高氯酸铵', enName: 'Ammonium perchlorate', cas: '7790-98-9', formula: 'NH₄ClO₄' },
                { name: '间苯二胺', enName: '1,3-Phenylenediamine', cas: '108-45-2', formula: 'C₆H₈N₂' },
                { name: '对苯二胺', enName: '1,4-Phenylenediamine', cas: '106-50-3', formula: 'C₆H₈N₂' },
                { name: '间甲酚', enName: '3-Methylphenol (m-Cresol)', cas: '108-39-4', formula: 'C₇H₈O' },
                { name: '四聚丙烯', enName: 'Tetrapolypropylene', cas: '6842-15-5', formula: 'C₁₂H₂₄' },
                { name: '间甲苯胺', enName: '3-Methylaniline (m-Toluidine)', cas: '108-44-1', formula: 'C₇H₉N' }
            ];

            const productTableBody = document.querySelector('#productTable tbody');
            const productSearchInput = document.getElementById('productSearch');

            function renderProducts(productsToRender) {
                productTableBody.innerHTML = '';
                if (productsToRender.length === 0) {
                    productTableBody.innerHTML = `<tr><td colspan="4" class="text-center py-8 text-brand-gray-500">未找到匹配的产品</td></tr>`;
                    return;
                }
                productsToRender.forEach((product, index) => {
                    const rowClass = index % 2 === 0 ? 'bg-white' : 'bg-gray-50';
                    productTableBody.innerHTML += `
                        <tr class="${rowClass} hover:bg-brand-blue-100 transition-colors duration-200">
                            <td class="px-6 py-3">${product.name}</td>
                            <td class="px-6 py-3 hidden md:table-cell">${product.enName}</td>
                            <td class="px-6 py-3">${product.cas}</td>
                            <td class="px-6 py-3 hidden sm:table-cell">${product.formula}</td>
                        </tr>`;
                });
            }

            function filterProducts() {
                const searchTerm = productSearchInput.value.toLowerCase();
                const filteredProducts = products.filter(p =>
                    p.name.toLowerCase().includes(searchTerm) ||
                    p.enName.toLowerCase().includes(searchTerm) ||
                    p.cas.toLowerCase().includes(searchTerm)
                );
                renderProducts(filteredProducts);
            }

            productSearchInput.addEventListener('keyup', filterProducts);
            renderProducts(products);

            // 专家团队数据及功能
            const experts = [
                {
                    id: 'zhaoqingjie',
                    name: '赵庆杰',
                    title: '特聘青年研究员 / 硕导',
                    image: './images/18-expert-zhao.jpg',
                    summary: '研究方向为基于天然产物的新药发现、基于蛋白修饰的抑制剂发现及活性研究、连续流技术的研究及应用。',
                    details: [
                        '<strong>教育背景:</strong> 2008年7月毕业于中国科学院上海药物研究所，获药物化学博士学位。',
                        '<strong>工作经历:</strong> 先后在美国内布拉斯加大学医学中心、康奈尔大学和密歇根大学从事博士后研究，2014年至2022年就职于中国科学院上海药物研究所。',
                        '<strong>学术成就:</strong> 在Nature Chemical Biology, Journal of Medicinal Chemistry等国际知名学术期刊上联合发表SCI学术论文30余篇，受邀参与编写英文专著1部；申请专利11项，获得授权专利8项。',
                        '<strong>主持项目:</strong> 作为课题或子课题负责人，主持研究课题8项，包括科技部“重大新药创制”国家科技重大专项，中国科学院战略性先导科技专项等。',
                        '<strong>荣誉:</strong> 2016年被评为上海市“浦江人才”。'
                    ]
                },
                {
                    id: 'wanghongxing',
                    name: '王红星',
                    title: '教授 / 博导 / “海河学者”',
                    image: './images/19-expert-wang.jpg',
                    summary: '主要从事精馏、反应精馏、化工过程系统工程、化工过程强化与数值模拟等领域的科学研究和成果工业化推广工作。',
                    details: [
                        '<strong>核心职称:</strong> 工学博士/博士后，教授，博士生导师。天津科技大学“海河学者”特聘教授，天津市高校中青年骨干创新人才，天津市创新人才推进计划中青年科技创新领军人才。',
                        '<strong>教育经历:</strong> 本、硕、博、博士后均就读于天津大学。',
                        '<strong>工业成就:</strong> 主持横向科研项目60余项，参与推广了近50套工业化装置。近5年来，共创造新增产值70亿元，新增利税10亿元。',
                        '<strong>学术成果:</strong> 在国内外权威刊物上发表相关研究论文70余篇，申请PCT-国际专利3项、国内专利100余项，其中授权国内发明专利75项。',
                        '<strong>荣誉奖励:</strong> 获福建省科学技术进步奖一等奖、中国石油和化学工业联合会科技进步奖一等奖等多个奖项。'
                    ]
                },
                {
                    id: 'yueqingbin',
                    name: '岳庆彬',
                    title: '高级工程师 / 总经理',
                    image: './images/20-expert-yue.jpg',
                    summary: '长期从事化工、石化、医药行业工程设计及管理工作，拥有丰富的项目管理和工程设计经验。',
                    details: [
                        '<strong>教育背景:</strong> 2004年毕业于大连理工大学，化机专业，学士学位。',
                        '<strong>职业经历:</strong>',
                        '2004年~2018年: 就职于中国天辰工程有限公司, 主要从事化工、石化、医药行业工程设计及管理工作。',
                        '2018年~2021年: 就职于天津开思工程科技有限公司, 任总经理。',
                        '2021年至今: 就职于山东富海石化工程有限公司天津分公司, 任总经理。',
                        '<strong>行业背景:</strong> 所属的山东富海石化工程有限公司具有化工、石化、医药行业甲级设计资质，已完成国内、国际工程项目案例上千项。'
                    ]
                },
                {
                    id: 'renyueqing',
                    name: '任跃青',
                    title: '工业AI首席架构师',
                    image: './images/21-expert-ren.jpg',
                    summary: '专注于将人工智能、工业互联网技术与能源及化工行业的核心业务场景深度融合。',
                    details: [
                        '<strong>核心优势:</strong> 拥有逾20年经验的复合型技术高管，成功主导设计并交付了多个国家级、行业级大型智慧平台。',
                        '<strong>技术领域:</strong> 精通AI算法、AIGC及工业大模型在安全风险预警、能耗优化及工艺流程模拟中的应用；具备构建复杂工业场景数字孪生体的能力。',
                        '<strong>项目经验:</strong> 作为项目总负责人，带领团队为能源大省山西打造了全国领先的省级能源信息监管与产业服务平台，解决了千万级测点的高并发数据采集和PB级海量数据处理等技术挑战。',
                        '<strong>数据治理:</strong> 建立了覆盖能源“采-储-运-销”全生命周期的统一数据治理体系，成功将原始数据转化为可信、可用、可追溯的数据资产。',
                        '<strong>产业赋能:</strong> 推动数据价值变现，通过开放API为金融、物流等产业链上下游企业提供数据服务，催生多种能源数据创新应用。'
                    ]
                }
            ];

            const expertCardsContainer = document.getElementById('expert-cards-container');
            const expertModal = document.getElementById('expert-modal');
            const modalContent = document.getElementById('modal-content');

            if (expertCardsContainer && expertModal && modalContent) {

                const renderCards = () => {
                    experts.forEach(expert => {
                        const card = document.createElement('div');
                        card.className = 'bg-white p-6 rounded-lg text-center shadow-lg transform hover:-translate-y-2 transition-transform duration-300 cursor-pointer expert-card-item';
                        card.innerHTML = `
                            <img src="${expert.image}" alt="${expert.name}" class="expert-avatar rounded-full mx-auto mb-4" onerror="this.onerror=null;this.src='https://placehold.co/128x128/E6F0FA/0A2D57?text=${expert.name.substring(0, 1)}';">
                            <h3 class="text-xl font-bold text-brand-blue-900">${expert.name}</h3>
                            <p class="text-brand-gray-500">${expert.title}</p>
                        `;
                        card.addEventListener('click', () => openExpertModal(expert));
                        expertCardsContainer.appendChild(card);
                    });
                }

                renderCards();
                renderCards(); // Render a second time for seamless scrolling

                // Modal functions
                function openExpertModal(expert) {
                    modalContent.innerHTML = `
                        <div class="p-6 md:p-8 relative">
                            <button id="close-modal-button" class="absolute top-4 right-4 text-gray-500 hover:text-gray-800">
                                <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
                            </button>
                            <div class="flex flex-col md:flex-row items-center md:items-start gap-6">
                                <img src="${expert.image}" alt="${expert.name}" class="w-32 h-32 md:w-40 md:h-40 object-cover rounded-full flex-shrink-0" onerror="this.onerror=null;this.src='https://placehold.co/160x160/E6F0FA/0A2D57?text=${expert.name.substring(0, 1)}';">
                                <div class="text-center md:text-left">
                                    <h2 class="text-3xl font-bold text-brand-blue-900">${expert.name}</h2>
                                    <p class="text-brand-blue-600 text-lg">${expert.title}</p>
                                    <p class="mt-2 text-brand-gray-800">${expert.summary}</p>
                                </div>
                            </div>
                            <div class="mt-6 border-t pt-6">
                                <h4 class="text-xl font-semibold text-brand-blue-900 mb-3">详细介绍</h4>
                                <ul class="list-disc list-inside space-y-2 text-brand-gray-800">
                                    ${expert.details.map(item => `<li>${item}</li>`).join('')}
                                </ul>
                            </div>
                        </div>
                    `;
                    expertModal.classList.remove('hidden', 'modal-enter-from');
                    modalContent.classList.remove('modal-content-enter-from');
                    document.body.style.overflow = 'hidden'; // Prevent background scrolling
                    document.getElementById('close-modal-button').addEventListener('click', closeExpertModal);
                }

                function closeExpertModal() {
                    expertModal.classList.add('modal-leave-to');
                    modalContent.classList.add('modal-content-leave-to');
                    setTimeout(() => {
                        expertModal.classList.add('hidden');
                        expertModal.classList.remove('modal-leave-to');
                        modalContent.classList.remove('modal-content-leave-to');
                        expertModal.classList.add('modal-enter-from');
                        modalContent.classList.add('modal-content-enter-from');
                        document.body.style.overflow = ''; // Restore background scrolling
                    }, 300);
                }
                expertModal.addEventListener('click', (e) => {
                    if (e.target === expertModal) {
                        closeExpertModal();
                    }
                });
            }


            // 移动菜单切换
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            const openIcon = document.getElementById('menu-open-icon');
            const closeIcon = document.getElementById('menu-close-icon');

            mobileMenuButton.addEventListener('click', () => {
                const isHidden = mobileMenu.classList.toggle('hidden');
                openIcon.classList.toggle('hidden', !isHidden);
                closeIcon.classList.toggle('hidden', isHidden);
            });

            document.querySelectorAll('#mobile-menu a').forEach(link => {
                link.addEventListener('click', () => {
                    mobileMenu.classList.add('hidden');
                    openIcon.classList.remove('hidden');
                    closeIcon.classList.add('hidden');
                });
            });

            // 导航栏滚动效果
            const header = document.getElementById('header');
            let lastScrollTop = 0;
            window.addEventListener('scroll', () => {
                let scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                if (scrollTop > 80 && scrollTop > lastScrollTop) {
                    header.style.top = '-100px';
                } else {
                    header.style.top = '0';
                }
                lastScrollTop = scrollTop <= 0 ? 0 : scrollTop;
            });

            // 平滑滚动到锚点
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const targetElement = document.querySelector(this.getAttribute('href'));
                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth'
                        });
                    }
                });
            });
        });
    </script>

</body>

</html>